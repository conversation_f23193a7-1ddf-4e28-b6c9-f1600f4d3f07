import { useState } from 'react';
import { Button } from '@/components/ui';
import { MizzyStarLayout } from '@/components/layout';
import { SimpleTestLayout } from '@/components/layout/SimpleTestLayout';
import { CatalogPanel, GalleryPanel, WorkbenchPanel, InfoPanel } from '@/components/panels';
import { useCases, useFiles, useTags, useDeleteFile } from '@/hooks';
// 注意：useAppStore 现在在 MinimalMizzyStarApp 中直接实现
// 这个文件暂时使用 useState 作为临时解决方案
// import { useAppStore } from '@/store';

/**
 * MizzyStar 新星计划 - 主应用组件
 *
 * 基于原始蓝图重新构建的全新应用界面：
 * 1. 四栏可调整大小布局
 * 2. MizzyStar 配色方案
 * 3. 完整的数据流和状态管理
 * 4. 现代化的用户体验
 */
function MizzyStarApp() {
  // ========================================
  // 全局状态管理 - 使用新的 AppStore
  // ========================================
  const [showDevControls, setShowDevControls] = useState(true);

  // 临时使用 useState - 等待任务 2.1 完成后统一迁移到新的 AppStore
  const [isCatalogVisible, setIsCatalogVisible] = useState(true);
  const [isInfoPanelVisible, setIsInfoPanelVisible] = useState(true);
  const [isWorkbenchVisible, setIsWorkbenchVisible] = useState(false);
  const [isFullscreenGallery, setIsFullscreenGallery] = useState(false);
  const [catalogPanelWidth, setCatalogPanelWidth] = useState(280);
  const [infoPanelWidth, setInfoPanelWidth] = useState(320);
  const [workbenchHeight, setWorkbenchHeight] = useState(200);

  // 数据选择状态
  const [selectedCaseId, setSelectedCaseId] = useState<number | null>(null);
  const [selectedFileIds, setSelectedFileIds] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeWorkbench, setActiveWorkbench] = useState<'clipboard' | 'batch' | 'export'>('clipboard');
  const [galleryLayout, setGalleryLayout] = useState<'grid' | 'list' | 'masonry'>('grid');
  const [galleryZoomLevel, setGalleryZoomLevel] = useState(50);
  const [showFileName, setShowFileName] = useState(true);
  const [showFileInfo, setShowFileInfo] = useState(false);

  // 临时操作方法
  const toggleCatalog = () => setIsCatalogVisible(prev => !prev);
  const toggleInfoPanel = () => setIsInfoPanelVisible(prev => !prev);
  const toggleWorkbench = () => setIsWorkbenchVisible(prev => !prev);
  const toggleFullscreenGallery = () => setIsFullscreenGallery(prev => !prev);
  const setSelectedCase = (caseId: number | null) => setSelectedCaseId(caseId);
  const toggleFileSelection = (fileId: number) => {
    setSelectedFileIds(prev =>
      prev.includes(fileId)
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  // ========================================
  // TanStack Query 数据获取
  // ========================================
  const { data: cases, isLoading: casesLoading, error: casesError } = useCases();

  // 当前选中的案例（默认选择第一个）
  const currentCaseId = selectedCaseId || cases?.[0]?.id || null;

  // 获取文件列表（基于当前案例和搜索条件）
  const { data: filesData, isLoading: filesLoading, error: filesError } = useFiles({
    case_id: currentCaseId || undefined,
    search: searchQuery || undefined,
  });

  // 获取标签列表
  const { isLoading: tagsLoading, error: tagsError } = useTags(currentCaseId || undefined);

  // 删除文件 mutation
  const deleteFileMutation = useDeleteFile();

  // ========================================
  // 数据处理和错误处理
  // ========================================

  // 处理加载状态
  const isLoading = casesLoading || filesLoading || tagsLoading;

  // 处理错误状态
  const hasError = casesError || filesError || tagsError;

  // 获取文件列表
  const files = filesData?.files || [];

  // 获取当前选中的文件
  const selectedFile = files.find(file =>
    selectedFileIds.includes(file.id)
  );

  // ========================================
  // 事件处理函数
  // ========================================

  const handleFileSelect = (fileId: number, selected: boolean) => {
    toggleFileSelection(fileId);
  };

  const handleFileDoubleClick = (file: any) => {
    console.log('预览文件:', file.fileName);
    // 这里可以添加文件预览逻辑
  };

  const handleFileDelete = (file: any) => {
    // 确认删除对话框
    const confirmed = window.confirm(`确定要删除文件 "${file.fileName}" 吗？\n\n此操作将把文件移动到回收站。`);

    if (confirmed && currentCaseId) {
      console.log('🗑️ 删除文件:', file.fileName, 'from case', currentCaseId);

      deleteFileMutation.mutate(
        { caseId: currentCaseId, fileId: file.id },
        {
          onSuccess: () => {
            console.log('✅ 文件删除成功:', file.fileName);
            // 如果删除的是当前选中的文件，清除选择
            if (selectedFileIds.includes(file.id)) {
              toggleFileSelection(file.id);
            }
          },
          onError: (error) => {
            console.error('❌ 文件删除失败:', error);
            alert(`删除文件失败: ${error.message}`);
          },
        }
      );
    }
  };

  // 布局切换控制
  const togglePanel = (panel: 'catalog' | 'workbench' | 'info' | 'fullscreen') => {
    switch (panel) {
      case 'catalog':
        toggleCatalog();
        break;
      case 'workbench':
        toggleWorkbench();
        if (!isWorkbenchVisible) {
          setActiveWorkbench('clipboard');
        }
        break;
      case 'info':
        toggleInfoPanel();
        break;
      case 'fullscreen':
        toggleFullscreenGallery();
        break;
    }
  };

  // ========================================
  // 错误和加载状态处理
  // ========================================

  if (hasError) {
    return (
      <div className="h-screen w-screen bg-[#191012] flex items-center justify-center">
        <div className="text-center text-[#A49F9A]">
          <h1 className="text-2xl font-bold mb-4">⚠️ 加载错误</h1>
          <p className="text-[#6B6B6B] mb-4">
            {casesError?.message || filesError?.message || tagsError?.message || '未知错误'}
          </p>
          <Button onClick={() => window.location.reload()}>
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-screen bg-[#191012]">
      {/* 开发控制面板 - 仅在非全屏模式显示 */}
      {!isFullscreenGallery && showDevControls && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-[#040709] border border-[#2A2A2A] rounded-lg p-2 shadow-lg">
          <div className="flex gap-2">
            <Button
              variant={isCatalogVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('catalog')}
            >
              📚 目录栏
            </Button>
            <Button
              variant={isWorkbenchVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('workbench')}
            >
              🛠️ 工作台
            </Button>
            <Button
              variant={isInfoPanelVisible ? "primary" : "outline"}
              size="sm"
              onClick={() => togglePanel('info')}
            >
              📄 信息栏
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => togglePanel('fullscreen')}
            >
              🔍 全屏
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowDevControls(false)}
            >
              ✕ 隐藏
            </Button>
            {isLoading && (
              <div className="flex items-center gap-2 px-2">
                <div className="w-4 h-4 border-2 border-[#E8E3E0] border-t-transparent rounded-full animate-spin" />
                <span className="text-sm text-[#6B6B6B]">加载中...</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 简单测试布局 */}
      <SimpleTestLayout
        catalogPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">📚 目录栏</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">案例: {cases?.find(c => c.id === currentCaseId)?.case_name || "加载中..."}</div>
              <div className="p-2 bg-[#2A2A2A] rounded">搜索: {searchQuery || "无"}</div>
              <div className="p-2 bg-[#2A2A2A] rounded">标签数量: 0</div>
            </div>
          </div>
        }
        galleryPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">🖼️ 画廊面板</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">文件数量: {files.length}</div>
              <div className="p-2 bg-[#2A2A2A] rounded">选中文件: {selectedFileIds.length}</div>
              <div className="p-2 bg-[#2A2A2A] rounded">布局: {galleryLayout}</div>
              <div className="p-2 bg-[#2A2A2A] rounded">缩放: {galleryZoomLevel}%</div>
              <div className="p-2 bg-[#2A2A2A] rounded">加载状态: {filesLoading ? "加载中" : "完成"}</div>
            </div>
            {files.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">文件列表:</h4>
                <div className="space-y-1 max-h-40 overflow-auto">
                  {files.slice(0, 5).map(file => (
                    <div key={file.id} className="p-1 bg-[#2A2A2A] rounded text-sm">
                      {file.file_name}
                    </div>
                  ))}
                  {files.length > 5 && (
                    <div className="p-1 text-[#6B6B6B] text-sm">
                      ...还有 {files.length - 5} 个文件
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        }
        workbenchPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">🛠️ 工作台</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">当前工作台: {activeWorkbench}</div>
              <div className="p-2 bg-[#2A2A2A] rounded">状态: 就绪</div>
            </div>
          </div>
        }
        infoPanel={
          <div className="h-full p-4 text-[#A49F9A]">
            <h3 className="text-lg font-semibold mb-4">📄 信息栏</h3>
            <div className="space-y-2">
              <div className="p-2 bg-[#2A2A2A] rounded">选中文件数: {selectedFileIds.length}</div>
              {selectedFile && (
                <div className="space-y-1">
                  <div className="p-2 bg-[#2A2A2A] rounded text-sm">
                    <div className="font-medium">当前文件:</div>
                    <div>{selectedFile.file_name}</div>
                  </div>
                  <div className="p-2 bg-[#2A2A2A] rounded text-sm">
                    <div className="font-medium">文件类型:</div>
                    <div>{selectedFile.file_type}</div>
                  </div>
                  <div className="p-2 bg-[#2A2A2A] rounded text-sm">
                    <div className="font-medium">文件大小:</div>
                    <div>{Math.round(selectedFile.file_size / 1024)} KB</div>
                  </div>
                </div>
              )}
              {selectedFileIds.length === 0 && (
                <div className="p-2 bg-[#2A2A2A] rounded text-sm text-[#6B6B6B]">
                  未选择文件
                </div>
              )}
            </div>
          </div>
        }
        showCatalogPanel={isCatalogVisible}
        showWorkbench={isWorkbenchVisible}
        showInfoPanel={isInfoPanelVisible}
        isFullscreenGallery={isFullscreenGallery}
      />

      {/* 全屏模式退出按钮 */}
      {isFullscreenGallery && (
        <Button
          variant="primary"
          size="sm"
          className="absolute top-4 right-4 z-50"
          onClick={() => toggleFullscreenGallery()}
        >
          ✕ 退出全屏
        </Button>
      )}

      {/* 隐藏的开发控制按钮 */}
      {!showDevControls && !isFullscreenGallery && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 z-50 opacity-50 hover:opacity-100"
          onClick={() => setShowDevControls(true)}
        >
          ⚙️
        </Button>
      )}
    </div>
  );
}

export default MizzyStarApp;
